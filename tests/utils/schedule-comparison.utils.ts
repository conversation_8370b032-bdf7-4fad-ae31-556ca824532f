/**
 * Schedule Data Comparison and Validation Utilities
 * 
 * This module provides utility functions for comparing workshift schedule data
 * between configuration and display components, generating detailed diff reports,
 * and validating data consistency.
 */

import { expect } from '@playwright/test';
import type { NormalizedSchedule, DaySchedule, TimeSlot } from './workshift.utils';

/**
 * Interface representing a schedule comparison result
 */
interface ScheduleComparisonResult {
	isConsistent: boolean;
	inconsistencies: ScheduleInconsistency[];
	summary: string;
}

/**
 * Interface representing an inconsistency found during comparison
 */
interface ScheduleInconsistency {
	type: 'day_status' | 'time_slot' | 'missing_day' | 'extra_day' | 'time_count';
	day: string;
	field?: string;
	configValue?: any;
	displayValue?: any;
	message: string;
}

/**
 * Interface representing detailed schedule statistics
 */
interface ScheduleStatistics {
	totalDays: number;
	activeDays: number;
	inactiveDays: number;
	totalTimeSlots: number;
	averageTimeSlotsPerDay: number;
	workingDays: string[];
	offDays: string[];
}

/**
 * Compare two normalized schedules for consistency
 * @param configSchedule - Schedule data from configuration component
 * @param displaySchedule - Schedule data from display component
 * @returns ScheduleComparisonResult - Detailed comparison result
 */
export function compareSchedules(
	configSchedule: NormalizedSchedule,
	displaySchedule: NormalizedSchedule
): ScheduleComparisonResult {
	console.log('Comparing schedules for consistency...');
	
	const inconsistencies: ScheduleInconsistency[] = [];
	
	// Create maps for easier lookup
	const configDays = new Map<string, DaySchedule>();
	const displayDays = new Map<string, DaySchedule>();
	
	configSchedule.days.forEach(day => configDays.set(day.day, day));
	displaySchedule.days.forEach(day => displayDays.set(day.day, day));
	
	// Check for missing days
	configDays.forEach((configDay, dayName) => {
		if (!displayDays.has(dayName)) {
			inconsistencies.push({
				type: 'missing_day',
				day: dayName,
				message: `Day ${dayName} exists in configuration but not in display`
			});
		}
	});
	
	displayDays.forEach((displayDay, dayName) => {
		if (!configDays.has(dayName)) {
			inconsistencies.push({
				type: 'extra_day',
				day: dayName,
				message: `Day ${dayName} exists in display but not in configuration`
			});
		}
	});
	
	// Compare each day that exists in both schedules
	configDays.forEach((configDay, dayName) => {
		const displayDay = displayDays.get(dayName);
		
		if (displayDay) {
			// Compare day active status
			if (configDay.active !== displayDay.active) {
				inconsistencies.push({
					type: 'day_status',
					day: dayName,
					field: 'active',
					configValue: configDay.active,
					displayValue: displayDay.active,
					message: `Day ${dayName} active status mismatch: config=${configDay.active}, display=${displayDay.active}`
				});
			}
			
			// Compare time slots count
			if (configDay.times.length !== displayDay.times.length) {
				inconsistencies.push({
					type: 'time_count',
					day: dayName,
					field: 'times.length',
					configValue: configDay.times.length,
					displayValue: displayDay.times.length,
					message: `Day ${dayName} time slot count mismatch: config=${configDay.times.length}, display=${displayDay.times.length}`
				});
			}
			
			// Compare individual time slots
			const maxTimeSlots = Math.max(configDay.times.length, displayDay.times.length);
			for (let i = 0; i < maxTimeSlots; i++) {
				const configTime = configDay.times[i];
				const displayTime = displayDay.times[i];
				
				if (configTime && displayTime) {
					// Compare start time
					if (configTime.start !== displayTime.start) {
						inconsistencies.push({
							type: 'time_slot',
							day: dayName,
							field: `times[${i}].start`,
							configValue: configTime.start,
							displayValue: displayTime.start,
							message: `Day ${dayName} time slot ${i} start time mismatch: config="${configTime.start}", display="${displayTime.start}"`
						});
					}
					
					// Compare end time
					if (configTime.end !== displayTime.end) {
						inconsistencies.push({
							type: 'time_slot',
							day: dayName,
							field: `times[${i}].end`,
							configValue: configTime.end,
							displayValue: displayTime.end,
							message: `Day ${dayName} time slot ${i} end time mismatch: config="${configTime.end}", display="${displayTime.end}"`
						});
					}
				} else if (configTime && !displayTime) {
					inconsistencies.push({
						type: 'time_slot',
						day: dayName,
						field: `times[${i}]`,
						configValue: configTime,
						displayValue: undefined,
						message: `Day ${dayName} time slot ${i} exists in config but not in display`
					});
				} else if (!configTime && displayTime) {
					inconsistencies.push({
						type: 'time_slot',
						day: dayName,
						field: `times[${i}]`,
						configValue: undefined,
						displayValue: displayTime,
						message: `Day ${dayName} time slot ${i} exists in display but not in config`
					});
				}
			}
		}
	});
	
	// Generate summary
	const isConsistent = inconsistencies.length === 0;
	const summary = generateComparisonSummary(inconsistencies, configSchedule, displaySchedule);
	
	console.log(`Schedule comparison result: ${isConsistent ? 'CONSISTENT' : 'INCONSISTENT'}`);
	if (!isConsistent) {
		console.log(`Found ${inconsistencies.length} inconsistencies`);
		inconsistencies.forEach(inc => console.log(`  - ${inc.message}`));
	}
	
	return {
		isConsistent,
		inconsistencies,
		summary
	};
}

/**
 * Generate a detailed comparison summary
 * @param inconsistencies - List of found inconsistencies
 * @param configSchedule - Configuration schedule
 * @param displaySchedule - Display schedule
 * @returns string - Formatted summary
 */
function generateComparisonSummary(
	inconsistencies: ScheduleInconsistency[],
	configSchedule: NormalizedSchedule,
	displaySchedule: NormalizedSchedule
): string {
	const configStats = calculateScheduleStatistics(configSchedule);
	const displayStats = calculateScheduleStatistics(displaySchedule);
	
	let summary = '=== SCHEDULE COMPARISON SUMMARY ===\n\n';
	
	// Overall result
	summary += `Result: ${inconsistencies.length === 0 ? '✅ CONSISTENT' : '❌ INCONSISTENT'}\n`;
	summary += `Total Inconsistencies: ${inconsistencies.length}\n\n`;
	
	// Statistics comparison
	summary += '--- SCHEDULE STATISTICS ---\n';
	summary += `Configuration: ${configStats.activeDays}/${configStats.totalDays} active days, ${configStats.totalTimeSlots} time slots\n`;
	summary += `Display: ${displayStats.activeDays}/${displayStats.totalDays} active days, ${displayStats.totalTimeSlots} time slots\n\n`;
	
	// Working days comparison
	summary += '--- WORKING DAYS ---\n';
	summary += `Configuration: ${configStats.workingDays.join(', ') || 'None'}\n`;
	summary += `Display: ${displayStats.workingDays.join(', ') || 'None'}\n\n`;
	
	// Off days comparison
	summary += '--- OFF DAYS ---\n';
	summary += `Configuration: ${configStats.offDays.join(', ') || 'None'}\n`;
	summary += `Display: ${displayStats.offDays.join(', ') || 'None'}\n\n`;
	
	// Detailed inconsistencies
	if (inconsistencies.length > 0) {
		summary += '--- DETAILED INCONSISTENCIES ---\n';
		
		const groupedInconsistencies = groupInconsistenciesByType(inconsistencies);
		
		Object.entries(groupedInconsistencies).forEach(([type, incs]) => {
			summary += `\n${type.toUpperCase().replace('_', ' ')} ISSUES (${incs.length}):\n`;
			incs.forEach(inc => {
				summary += `  • ${inc.message}\n`;
			});
		});
	}
	
	return summary;
}

/**
 * Group inconsistencies by type for better reporting
 * @param inconsistencies - List of inconsistencies
 * @returns Object with inconsistencies grouped by type
 */
function groupInconsistenciesByType(inconsistencies: ScheduleInconsistency[]): { [key: string]: ScheduleInconsistency[] } {
	const grouped: { [key: string]: ScheduleInconsistency[] } = {};
	
	inconsistencies.forEach(inc => {
		if (!grouped[inc.type]) {
			grouped[inc.type] = [];
		}
		grouped[inc.type].push(inc);
	});
	
	return grouped;
}

/**
 * Calculate statistics for a schedule
 * @param schedule - Schedule to analyze
 * @returns ScheduleStatistics - Calculated statistics
 */
function calculateScheduleStatistics(schedule: NormalizedSchedule): ScheduleStatistics {
	const totalDays = schedule.days.length;
	const activeDays = schedule.days.filter(day => day.active).length;
	const inactiveDays = totalDays - activeDays;
	const totalTimeSlots = schedule.days.reduce((sum, day) => sum + day.times.length, 0);
	const averageTimeSlotsPerDay = totalDays > 0 ? totalTimeSlots / totalDays : 0;
	
	const workingDays = schedule.days
		.filter(day => day.active && day.times.length > 0)
		.map(day => day.day);
	
	const offDays = schedule.days
		.filter(day => !day.active || day.times.length === 0)
		.map(day => day.day);
	
	return {
		totalDays,
		activeDays,
		inactiveDays,
		totalTimeSlots,
		averageTimeSlotsPerDay,
		workingDays,
		offDays
	};
}

/**
 * Validate schedule data consistency using Playwright assertions
 * @param comparisonResult - Result from schedule comparison
 * @param options - Validation options
 */
export async function validateScheduleConsistency(
	comparisonResult: ScheduleComparisonResult,
	options: {
		allowTimeFormatDifferences?: boolean;
		allowMinorDiscrepancies?: boolean;
		maxAllowedInconsistencies?: number;
	} = {}
): Promise<void> {
	console.log('Validating schedule consistency...');
	
	const {
		allowTimeFormatDifferences = false,
		allowMinorDiscrepancies = false,
		maxAllowedInconsistencies = 0
	} = options;
	
	// Filter inconsistencies based on options
	let relevantInconsistencies = comparisonResult.inconsistencies;
	
	if (allowTimeFormatDifferences) {
		// Filter out time format differences if allowed
		relevantInconsistencies = relevantInconsistencies.filter(inc => 
			inc.type !== 'time_slot' || !isTimeFormatDifference(inc.configValue, inc.displayValue)
		);
	}
	
	if (allowMinorDiscrepancies) {
		// Filter out minor discrepancies if allowed
		relevantInconsistencies = relevantInconsistencies.filter(inc => 
			!isMinorDiscrepancy(inc)
		);
	}
	
	// Print summary for debugging
	console.log(comparisonResult.summary);
	
	// Validate against expectations
	if (maxAllowedInconsistencies === 0) {
		expect(relevantInconsistencies.length).toBe(0);
		expect(comparisonResult.isConsistent).toBe(true);
	} else {
		expect(relevantInconsistencies.length).toBeLessThanOrEqual(maxAllowedInconsistencies);
	}
	
	// If there are inconsistencies, provide detailed information
	if (relevantInconsistencies.length > 0) {
		console.log('\n❌ Schedule validation failed with inconsistencies:');
		relevantInconsistencies.forEach((inc, index) => {
			console.log(`${index + 1}. ${inc.message}`);
		});
		
		// Throw with detailed message if strict validation
		if (maxAllowedInconsistencies === 0) {
			throw new Error(
				`Schedule consistency validation failed with ${relevantInconsistencies.length} inconsistencies:\n` +
				relevantInconsistencies.map(inc => `• ${inc.message}`).join('\n')
			);
		}
	} else {
		console.log('✅ Schedule validation passed - data is consistent');
	}
}

/**
 * Check if an inconsistency is due to time format differences
 * @param configValue - Value from configuration
 * @param displayValue - Value from display
 * @returns boolean - True if it's a time format difference
 */
function isTimeFormatDifference(configValue: any, displayValue: any): boolean {
	if (typeof configValue !== 'string' || typeof displayValue !== 'string') {
		return false;
	}
	
	// Check if both values represent the same time in different formats
	const timePattern = /^\d{1,2}:\d{2}(\s*(AM|PM))?$/i;
	
	if (timePattern.test(configValue) && timePattern.test(displayValue)) {
		// Both are time strings, check if they represent the same time
		const configTime = parseTimeString(configValue);
		const displayTime = parseTimeString(displayValue);
		
		return configTime === displayTime;
	}
	
	return false;
}

/**
 * Parse time string to minutes since midnight for comparison
 * @param timeString - Time string to parse
 * @returns number - Minutes since midnight
 */
function parseTimeString(timeString: string): number {
	const [time, period] = timeString.split(/\s+/);
	const [hours, minutes] = time.split(':').map(Number);
	
	let totalMinutes = hours * 60 + minutes;
	
	if (period) {
		if (period.toUpperCase() === 'AM' && hours === 12) {
			totalMinutes -= 12 * 60;
		} else if (period.toUpperCase() === 'PM' && hours !== 12) {
			totalMinutes += 12 * 60;
		}
	}
	
	return totalMinutes;
}

/**
 * Check if an inconsistency is considered minor
 * @param inconsistency - Inconsistency to check
 * @returns boolean - True if it's a minor discrepancy
 */
function isMinorDiscrepancy(inconsistency: ScheduleInconsistency): boolean {
	// Define what constitutes a minor discrepancy
	const minorTypes = ['extra_day', 'missing_day'];
	
	return minorTypes.includes(inconsistency.type);
}

/**
 * Generate a detailed diff report between two schedules
 * @param configSchedule - Configuration schedule
 * @param displaySchedule - Display schedule
 * @returns string - Formatted diff report
 */
export function generateScheduleDiff(
	configSchedule: NormalizedSchedule,
	displaySchedule: NormalizedSchedule
): string {
	const comparison = compareSchedules(configSchedule, displaySchedule);
	
	let diff = '=== SCHEDULE DIFF REPORT ===\n\n';
	
	// Header information
	diff += `Configuration Source: ${configSchedule.source}\n`;
	diff += `Display Source: ${displaySchedule.source}\n`;
	diff += `Same as Business Hours: ${configSchedule.sameAsBusinessHours}\n\n`;
	
	// Day-by-day comparison
	diff += '--- DAY-BY-DAY COMPARISON ---\n';
	
	const allDays = new Set([
		...configSchedule.days.map(d => d.day),
		...displaySchedule.days.map(d => d.day)
	]);
	
	Array.from(allDays).sort().forEach(dayName => {
		const configDay = configSchedule.days.find(d => d.day === dayName);
		const displayDay = displaySchedule.days.find(d => d.day === dayName);
		
		diff += `\n${dayName.toUpperCase()}:\n`;
		
		if (configDay && displayDay) {
			diff += `  Active: ${configDay.active} → ${displayDay.active} ${configDay.active === displayDay.active ? '✅' : '❌'}\n`;
			diff += `  Time Slots: ${configDay.times.length} → ${displayDay.times.length} ${configDay.times.length === displayDay.times.length ? '✅' : '❌'}\n`;
			
			// Compare time slots
			const maxSlots = Math.max(configDay.times.length, displayDay.times.length);
			for (let i = 0; i < maxSlots; i++) {
				const configTime = configDay.times[i];
				const displayTime = displayDay.times[i];
				
				if (configTime && displayTime) {
					const startMatch = configTime.start === displayTime.start;
					const endMatch = configTime.end === displayTime.end;
					
					diff += `    Slot ${i + 1}: ${configTime.start}-${configTime.end} → ${displayTime.start}-${displayTime.end} ${startMatch && endMatch ? '✅' : '❌'}\n`;
				} else if (configTime) {
					diff += `    Slot ${i + 1}: ${configTime.start}-${configTime.end} → MISSING ❌\n`;
				} else if (displayTime) {
					diff += `    Slot ${i + 1}: MISSING → ${displayTime.start}-${displayTime.end} ❌\n`;
				}
			}
		} else if (configDay) {
			diff += `  EXISTS IN CONFIG ONLY ❌\n`;
			diff += `  Active: ${configDay.active}, Time Slots: ${configDay.times.length}\n`;
		} else if (displayDay) {
			diff += `  EXISTS IN DISPLAY ONLY ❌\n`;
			diff += `  Active: ${displayDay.active}, Time Slots: ${displayDay.times.length}\n`;
		}
	});
	
	// Summary
	diff += `\n--- SUMMARY ---\n`;
	diff += `Total Inconsistencies: ${comparison.inconsistencies.length}\n`;
	diff += `Overall Result: ${comparison.isConsistent ? '✅ CONSISTENT' : '❌ INCONSISTENT'}\n`;
	
	return diff;
}

/**
 * Assert that two schedules are consistent
 * @param configSchedule - Configuration schedule
 * @param displaySchedule - Display schedule
 * @param options - Assertion options
 */
export async function assertSchedulesConsistent(
	configSchedule: NormalizedSchedule,
	displaySchedule: NormalizedSchedule,
	options: {
		allowTimeFormatDifferences?: boolean;
		allowMinorDiscrepancies?: boolean;
		maxAllowedInconsistencies?: number;
	} = {}
): Promise<void> {
	const comparison = compareSchedules(configSchedule, displaySchedule);
	await validateScheduleConsistency(comparison, options);
}

// Export types for use in tests
export type {
	ScheduleComparisonResult,
	ScheduleInconsistency,
	ScheduleStatistics
};