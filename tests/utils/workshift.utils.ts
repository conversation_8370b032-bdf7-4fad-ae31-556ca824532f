/**
 * Workshift Schedule Data Extraction and Normalization Utilities
 * 
 * This module provides utility functions for extracting workshift schedule data
 * from both the configuration component (WorkshiftSection.svelte) and display
 * component (users/+page.svelte), and normalizing the data for comparison.
 */

import type { Page, Locator } from '@playwright/test';
import { expect } from '@playwright/test';

/**
 * Interface representing a normalized time slot
 */
export interface TimeSlot {
	start: string;
	end: string;
}

/**
 * Interface representing a normalized day schedule
 */
export interface DaySchedule {
	day: string;
	active: boolean;
	times: TimeSlot[];
}

/**
 * Interface representing a complete normalized schedule
 */
export interface NormalizedSchedule {
	days: DaySchedule[];
	sameAsBusinessHours: boolean;
	source: 'configuration' | 'display';
}

/**
 * Interface representing tooltip schedule display data
 */
interface TooltipScheduleData {
	[dayKey: string]: string; // e.g., "day_monday": "9:00 AM - 5:00 PM"
}

/**
 * Navigate to user's workshift settings page
 * @param page - Playwright page object
 */
export async function navigateToWorkshiftSettings(page: Page): Promise<void> {
	console.log('Navigating to workshift settings page...');
	
	// Navigate to settings/account page
	await page.goto('/settings/account');
	await page.waitForTimeout(1000);
	
	// Verify we're on the correct page
	await expect(page).toHaveURL('/settings/account');

	// Click on workshift settings tab
	const workshiftTab = page.locator('#settings-account-tab-schedule');
	await expect(workshiftTab).toBeVisible({ timeout: 10000 });
	await workshiftTab.click();
	await page.waitForTimeout(1000);
	
	// Wait for workshift form to load
	const workshiftForm = page.locator('#settings-user-workshift-form');
	await expect(workshiftForm).toBeVisible({ timeout: 10000 });
	
	console.log('✓ Successfully navigated to workshift settings page');
}

/**
 * Check if the workshift toggle is enabled (same as business hours)
 * @param page - Playwright page object
 * @returns Promise<boolean> - True if same as business hours is enabled
 */
export async function isBusinessHoursToggleEnabled(page: Page): Promise<boolean> {
	const toggle = page.locator('#settings-user-workshift-business-hours-toggle');
	await expect(toggle).toBeVisible({ timeout: 10000 });
	
	const isChecked = await toggle.isChecked();
	console.log(`Business hours toggle is ${isChecked ? 'enabled' : 'disabled'}`);
	return isChecked;
}

/**
 * Extract schedule configuration from WorkshiftSection.svelte
 * @param page - Playwright page object
 * @returns Promise<NormalizedSchedule> - Normalized schedule data
 */
export async function extractConfigurationSchedule(page: Page): Promise<NormalizedSchedule> {
	console.log('Extracting schedule configuration from WorkshiftSection...');
	
	// Check if same as business hours is enabled
	const sameAsBusinessHours = await isBusinessHoursToggleEnabled(page);
	
	const days: DaySchedule[] = [];
	const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
	
	// Extract data for each day
	for (let dayIndex = 0; dayIndex < dayNames.length; dayIndex++) {
		const dayName = dayNames[dayIndex];
		
		// Determine if we're looking at readonly (business hours) or editable schedule
		const isReadonly = sameAsBusinessHours;
		
		// Get day checkbox (different selectors for readonly vs editable)
		let dayCheckbox: Locator;
		if (isReadonly) {
			// For readonly display, try both mobile and desktop selectors
			const mobileCheckbox = page.locator(`#settings-user-workshift-readonly-day-mobile-${dayIndex}`);
			const desktopCheckbox = page.locator(`#settings-user-workshift-readonly-day-desktop-${dayIndex}`);
			
			// Check which one is visible
			const mobileVisible = await mobileCheckbox.isVisible().catch(() => false);
			const desktopVisible = await desktopCheckbox.isVisible().catch(() => false);
			
			if (mobileVisible) {
				dayCheckbox = mobileCheckbox;
			} else if (desktopVisible) {
				dayCheckbox = desktopCheckbox;
			} else {
				// Fallback: try to find any checkbox for this day
				dayCheckbox = page.locator(`[id*="readonly-day"][id*="${dayIndex}"]`).first();
			}
		} else {
			// For editable schedule, try both mobile and desktop selectors
			const mobileCheckbox = page.locator(`#settings-user-workshift-day-mobile-${dayIndex}`);
			const desktopCheckbox = page.locator(`#settings-user-workshift-day-desktop-${dayIndex}`);
			
			// Check which one is visible
			const mobileVisible = await mobileCheckbox.isVisible().catch(() => false);
			const desktopVisible = await desktopCheckbox.isVisible().catch(() => false);
			
			if (mobileVisible) {
				dayCheckbox = mobileCheckbox;
			} else if (desktopVisible) {
				dayCheckbox = desktopCheckbox;
			} else {
				// Fallback: try to find any checkbox for this day
				dayCheckbox = page.locator(`[id*="day"][id*="${dayIndex}"]`).first();
			}
		}
		
		// Check if day is active
		let isActive = false;
		try {
			await expect(dayCheckbox).toBeVisible({ timeout: 5000 });
			isActive = await dayCheckbox.isChecked();
		} catch (error) {
			console.log(`Warning: Could not find checkbox for day ${dayName} (index ${dayIndex})`);
			isActive = false;
		}
		
		// Extract time slots for this day
		const times: TimeSlot[] = [];
		
		if (isActive) {
			// Try to find time slots
			let timeSlotIndex = 0;
			let foundTimeSlot = true;
			
			while (foundTimeSlot && timeSlotIndex < 10) { // Limit to 10 time slots per day
				let startTimeSelector: string;
				let endTimeSelector: string;
				
				if (isReadonly) {
					// For readonly, time slots are displayed as spans, not selects
					// We need to extract from the visible text
					const timeSlotContainer = page.locator(`#settings-user-workshift-readonly-day-${dayIndex}`);
					if (await timeSlotContainer.isVisible()) {
						// Extract time information from the readonly display
						const timeText = await timeSlotContainer.textContent();
						if (timeText) {
							// Parse time ranges from the text (e.g., "09:00 to 17:00")
							const timeMatches = timeText.match(/(\d{2}:\d{2})\s*(?:to|-)?\s*(\d{2}:\d{2})/g);
							if (timeMatches) {
								for (const match of timeMatches) {
									const [, start, end] = match.match(/(\d{2}:\d{2})\s*(?:to|-)?\s*(\d{2}:\d{2})/) || [];
									if (start && end) {
										times.push({ start, end });
									}
								}
							}
						}
					}
					foundTimeSlot = false; // Exit loop for readonly
				} else {
					// For editable schedule, extract from select elements
					// Try mobile first, then desktop
					const mobileStartSelector = `#settings-user-workshift-mobile-start-${dayIndex}-${timeSlotIndex}`;
					const mobileEndSelector = `#settings-user-workshift-mobile-end-${dayIndex}-${timeSlotIndex}`;
					const desktopStartSelector = `#settings-user-workshift-desktop-start-${dayIndex}-${timeSlotIndex}`;
					const desktopEndSelector = `#settings-user-workshift-desktop-end-${dayIndex}-${timeSlotIndex}`;
					
					const mobileStartElement = page.locator(mobileStartSelector);
					const desktopStartElement = page.locator(desktopStartSelector);
					
					let startElement: Locator;
					let endElement: Locator;
					
					if (await mobileStartElement.isVisible().catch(() => false)) {
						startElement = mobileStartElement;
						endElement = page.locator(mobileEndSelector);
					} else if (await desktopStartElement.isVisible().catch(() => false)) {
						startElement = desktopStartElement;
						endElement = page.locator(desktopEndSelector);
					} else {
						foundTimeSlot = false;
						break;
					}
					
					try {
						const startTime = await startElement.inputValue();
						const endTime = await endElement.inputValue();
						
						if (startTime && endTime) {
							times.push({ start: startTime, end: endTime });
						}
					} catch (error) {
						foundTimeSlot = false;
					}
				}
				
				timeSlotIndex++;
			}
		}
		
		days.push({
			day: dayName,
			active: isActive,
			times: times
		});
		
		console.log(`  ${dayName}: active=${isActive}, times=${times.length}`);
	}
	
	const schedule: NormalizedSchedule = {
		days,
		sameAsBusinessHours,
		source: 'configuration'
	};
	
	console.log('✓ Configuration schedule extracted successfully');
	return schedule;
}

/**
 * Navigate to users page and find a specific user
 * @param page - Playwright page object
 * @param userId - User ID to find (optional, will find first user if not provided)
 * @returns Promise<string> - The user ID found
 */
export async function navigateToUsersPageAndFindUser(page: Page, userId?: string): Promise<string> {
	console.log('Navigating to users page...');
	
	// Navigate to users page
	await page.goto('/users');
	await page.waitForTimeout(1000);
	await expect(page).toHaveURL('/users');
	
	// Wait for users table to load
	const usersTable = page.locator('#users-page-table');
	await expect(usersTable).toBeVisible({ timeout: 10000 });
	
	// If no specific user ID provided, find the first available user
	if (!userId) {
		const firstUserRow = page.locator('#users-page-table-body tr').first();
		await expect(firstUserRow).toBeVisible({ timeout: 10000 });
		
		// Extract user ID from the first row
		const userIdCell = firstUserRow.locator('td').first();
		const userIdText = await userIdCell.textContent();
		userId = userIdText?.trim() || '1';
		
		console.log(`Using first available user: ${userId}`);
	}
	
	// Verify user exists in the table
	const userRow = page.locator(`#users-page-table-row-${userId}`);
	await expect(userRow).toBeVisible({ timeout: 10000 });
	
	console.log(`✓ Found user ${userId} in users table`);
	return userId;
}

/**
 * Extract schedule data from users page tooltip
 * @param page - Playwright page object
 * @param userId - User ID to extract schedule for
 * @returns Promise<NormalizedSchedule> - Normalized schedule data
 */
export async function extractDisplaySchedule(page: Page, userId: string): Promise<NormalizedSchedule> {
	console.log(`Extracting schedule display data for user ${userId}...`);
	
	// Find the workshift cell for this user
	const workshiftCell = page.locator(`#users-page-table-cell-work-shift-${userId}`);
	await expect(workshiftCell).toBeVisible({ timeout: 10000 });
	
	// Look for the clock icon (schedule trigger)
	const clockIcon = workshiftCell.locator('[data-popover-target^="popover-workshift-"]');
	
	// Check if schedule exists
	const hasSchedule = await clockIcon.isVisible().catch(() => false);
	
	if (!hasSchedule) {
		console.log('  No schedule found for this user');
		return {
			days: [],
			sameAsBusinessHours: false,
			source: 'display'
		};
	}
	
	// Hover over the clock icon to show tooltip
	await clockIcon.hover();
	await page.waitForTimeout(1000); // Wait for tooltip to appear
	
	// Find the tooltip content
	const tooltip = page.locator(`[data-popover-target="popover-workshift-${userId}"]`).locator('xpath=following-sibling::*').first();
	await expect(tooltip).toBeVisible({ timeout: 5000 });
	
	// Extract schedule data from tooltip
	const tooltipContent = await tooltip.textContent();
	console.log(`  Tooltip content: ${tooltipContent}`);
	
	// Parse the tooltip content to extract schedule data
	const days: DaySchedule[] = [];
	const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
	
	// Look for each day in the tooltip content
	for (const dayName of dayNames) {
		const dayKey = `day_${dayName}`;
		
		// Find the day row in the tooltip
		const dayRow = tooltip.locator(`text=${dayKey}`).locator('xpath=ancestor::div[1]');
		
		let isActive = false;
		const times: TimeSlot[] = [];
		
		if (await dayRow.isVisible().catch(() => false)) {
			const dayRowText = await dayRow.textContent();
			
			if (dayRowText) {
				// Check if day is marked as "off"
				if (dayRowText.includes('off')) {
					isActive = false;
				} else {
					isActive = true;
					
					// Extract time slots from the day row
					// Look for time patterns like "9:00 AM - 5:00 PM" or "09:00 - 17:00"
					const timePatterns = [
						/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*-\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/gi,
						/(\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})/gi
					];
					
					for (const pattern of timePatterns) {
						const matches = dayRowText.match(pattern);
						if (matches) {
							for (const match of matches) {
								const [, start, end] = match.match(/([^-]+)-([^-]+)/) || [];
								if (start && end) {
									times.push({
										start: start.trim(),
										end: end.trim()
									});
								}
							}
							break; // Found matches, no need to try other patterns
						}
					}
				}
			}
		}
		
		days.push({
			day: dayName,
			active: isActive,
			times: times
		});
		
		console.log(`  ${dayName}: active=${isActive}, times=${times.length}`);
	}
	
	// Hide tooltip
	await page.mouse.move(0, 0);
	await page.waitForTimeout(500);
	
	const schedule: NormalizedSchedule = {
		days,
		sameAsBusinessHours: false, // We can't determine this from display
		source: 'display'
	};
	
	console.log('✓ Display schedule extracted successfully');
	return schedule;
}

/**
 * Convert 12-hour time format to 24-hour format
 * @param timeString - Time in 12-hour format (e.g., "9:00 AM")
 * @returns string - Time in 24-hour format (e.g., "09:00")
 */
export function convertTo24HourFormat(timeString: string): string {
	if (!timeString) return timeString;
	
	// If already in 24-hour format, return as is
	if (!/AM|PM/i.test(timeString)) {
		return timeString;
	}
	
	const [time, period] = timeString.split(/\s+/);
	const [hours, minutes] = time.split(':');
	
	let hour24 = parseInt(hours);
	
	if (period.toUpperCase() === 'AM' && hour24 === 12) {
		hour24 = 0;
	} else if (period.toUpperCase() === 'PM' && hour24 !== 12) {
		hour24 += 12;
	}
	
	return `${hour24.toString().padStart(2, '0')}:${minutes}`;
}

/**
 * Convert 24-hour time format to 12-hour format
 * @param timeString - Time in 24-hour format (e.g., "09:00")
 * @returns string - Time in 12-hour format (e.g., "9:00 AM")
 */
export function convertTo12HourFormat(timeString: string): string {
	if (!timeString) return timeString;
	
	// If already in 12-hour format, return as is
	if (/AM|PM/i.test(timeString)) {
		return timeString;
	}
	
	const [hours, minutes] = timeString.split(':');
	const hour24 = parseInt(hours);
	
	let hour12 = hour24;
	let period = 'AM';
	
	if (hour24 === 0) {
		hour12 = 12;
	} else if (hour24 === 12) {
		period = 'PM';
	} else if (hour24 > 12) {
		hour12 = hour24 - 12;
		period = 'PM';
	}
	
	return `${hour12}:${minutes} ${period}`;
}

/**
 * Normalize time format to 24-hour format for comparison
 * @param timeString - Time string in any format
 * @returns string - Time in 24-hour format
 */
export function normalizeTimeFormat(timeString: string): string {
	if (!timeString) return '';
	
	// Remove Thai time suffix if present
	const cleanTime = timeString.replace(/\s*น\.\s*$/, '');
	
	return convertTo24HourFormat(cleanTime);
}

/**
 * Normalize day name to standard format
 * @param dayName - Day name in any format
 * @returns string - Day name in lowercase format
 */
export function normalizeDayName(dayName: string): string {
	if (!dayName) return '';
	
	// Handle translation keys like "day_monday"
	if (dayName.startsWith('day_')) {
		return dayName.substring(4).toLowerCase();
	}
	
	// Handle various day name formats
	const normalized = dayName.toLowerCase().trim();
	
	const dayMapping: { [key: string]: string } = {
		'monday': 'monday',
		'mon': 'monday',
		'tuesday': 'tuesday',
		'tue': 'tuesday',
		'wednesday': 'wednesday',
		'wed': 'wednesday',
		'thursday': 'thursday',
		'thu': 'thursday',
		'friday': 'friday',
		'fri': 'friday',
		'saturday': 'saturday',
		'sat': 'saturday',
		'sunday': 'sunday',
		'sun': 'sunday'
	};
	
	return dayMapping[normalized] || normalized;
}

/**
 * Normalize schedule data to a standard format for comparison
 * @param schedule - Schedule data to normalize
 * @returns NormalizedSchedule - Normalized schedule data
 */
export function normalizeScheduleData(schedule: NormalizedSchedule): NormalizedSchedule {
	const normalizedDays: DaySchedule[] = schedule.days.map(day => ({
		day: normalizeDayName(day.day),
		active: day.active,
		times: day.times.map(time => ({
			start: normalizeTimeFormat(time.start),
			end: normalizeTimeFormat(time.end)
		}))
	}));
	
	return {
		...schedule,
		days: normalizedDays
	};
}